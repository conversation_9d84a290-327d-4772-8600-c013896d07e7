import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@/components/lib/ui";
import { useZiggy } from "@/hooks";
import { cn } from "@/utils";
import { router } from "@inertiajs/react";
import { useState } from "react";
import {
    IoMusicalNotes,
    IoMicOutline,
    IoAlbumsOutline,
    IoTrophyOutline,
    IoArrowForwardSharp,
    IoClose,
    IoCheckmark
} from "@/components/lib/icons";

interface WelcomeModalProps {
    isOpen: boolean;
    onClose: () => void;
    onComplete: () => void;
    userName?: string;
    isNewUser?: boolean;
}

export function WelcomeModal({ 
    isOpen, 
    onClose, 
    onComplete, 
    userName = "there",
    isNewUser = true 
}: WelcomeModalProps) {
    const route = useZiggy();
    const [currentStep, setCurrentStep] = useState(0);
    const [isClosing, setIsClosing] = useState(false);

    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
            setIsClosing(false);
            setCurrentStep(0);
        }, 300);
    };

    const handleComplete = () => {
        onComplete();
        handleClose();
    };

    const handleBecomeArtist = () => {
        onComplete();
        router.visit(route('app.settings.account'));
    };

    const steps = [
        {
            title: isNewUser ? `Welcome to Smovee, ${userName}!` : `Welcome back, ${userName}!`,
            subtitle: isNewUser 
                ? "We're excited to have you join our creative community" 
                : "We've missed you! Let's get you back into the rhythm",
            content: (
                <div className="text-center space-y-6">
                    <div className="flex justify-center">
                        <div className="w-20 h-20 bg-gradient-to-br from-primary to-primary-600 rounded-full flex items-center justify-center">
                            <IoMusicalNotes className="w-10 h-10 text-white" />
                        </div>
                    </div>
                    <div className="space-y-3">
                        <p className="text-gray-600 dark:text-gray-300 text-lg">
                            {isNewUser 
                                ? "Discover amazing music, connect with talented artists, and share your own creative journey."
                                : "Continue exploring amazing music and connecting with our growing community of artists."
                            }
                        </p>
                        <div className="grid grid-cols-3 gap-4 mt-6">
                            <FeatureCard
                                icon={IoMusicalNotes}
                                title="Discover"
                                description="Amazing music from talented artists"
                            />
                            <FeatureCard
                                icon={IoAlbumsOutline}
                                title="Listen"
                                description="High-quality audio streaming"
                            />
                            <FeatureCard
                                icon={IoTrophyOutline}
                                title="Connect"
                                description="Follow your favorite artists"
                            />
                        </div>
                    </div>
                </div>
            )
        },
        {
            title: "Ready to Share Your Talent?",
            subtitle: "Join our community of artists and showcase your creativity",
            content: (
                <div className="text-center space-y-6">
                    <div className="flex justify-center">
                        <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                            <IoMicOutline className="w-10 h-10 text-white" />
                        </div>
                    </div>
                    <div className="space-y-4">
                        <p className="text-gray-600 dark:text-gray-300 text-lg">
                            Since we're just launching, we're looking for talented artists to join our platform. 
                            This is a perfect opportunity to be among the first creators on Smovee!
                        </p>
                        <div className="bg-gradient-to-r from-primary/10 to-purple-500/10 rounded-lg p-6 border border-primary/20">
                            <h4 className="font-semibold text-lg mb-3 text-primary">Early Artist Benefits</h4>
                            <ul className="space-y-2 text-left">
                                <li className="flex items-center gap-2">
                                    <IoCheckmark className="w-5 h-5 text-green-500 flex-shrink-0" />
                                    <span className="text-sm">Priority review for artist applications</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <IoCheckmark className="w-5 h-5 text-green-500 flex-shrink-0" />
                                    <span className="text-sm">Featured placement for early adopters</span>
                                </li>
                                <li className="flex items-center gap-2">
                                    <IoCheckmark className="w-5 h-5 text-green-500 flex-shrink-0" />
                                    <span className="text-sm">Direct feedback and support from our team</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            )
        }
    ];

    const currentStepData = steps[currentStep];

    return (
        <Modal isOpen={isOpen} closeModal={handleClose}>
            <ModalBody 
                className={cn(
                    "w-11/12 md:w-2/3 lg:w-1/2 xl:w-2/5 max-w-2xl p-0 overflow-hidden",
                    "transform transition-all duration-300 ease-out",
                    isClosing && "scale-95 opacity-0"
                )}
                onClose={handleClose}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-primary to-primary-600 text-white p-6 relative">
                    <button
                        onClick={handleClose}
                        className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-full transition-colors"
                    >
                        <IoClose className="w-5 h-5" />
                    </button>
                    <div className="pr-12">
                        <h2 className="text-2xl font-bold mb-2">{currentStepData.title}</h2>
                        <p className="text-primary-100 text-sm">{currentStepData.subtitle}</p>
                    </div>
                </div>

                {/* Content */}
                <div className="p-8">
                    {currentStepData.content}
                </div>

                {/* Footer */}
                <div className="px-8 pb-8">
                    <div className="flex items-center justify-between">
                        {/* Step indicators */}
                        <div className="flex space-x-2">
                            {steps.map((_, index) => (
                                <div
                                    key={index}
                                    className={cn(
                                        "w-2 h-2 rounded-full transition-colors",
                                        index === currentStep 
                                            ? "bg-primary" 
                                            : "bg-gray-300 dark:bg-gray-600"
                                    )}
                                />
                            ))}
                        </div>

                        {/* Action buttons */}
                        <div className="flex gap-3">
                            {currentStep === 0 && (
                                <>
                                    <Button
                                        variant="outline"
                                        onClick={handleComplete}
                                        className="px-6"
                                    >
                                        Skip for now
                                    </Button>
                                    <Button
                                        variant="primary"
                                        onClick={() => setCurrentStep(1)}
                                        className="px-6 flex items-center gap-2"
                                    >
                                        Continue
                                        <IoArrowForwardSharp className="w-4 h-4" />
                                    </Button>
                                </>
                            )}
                            {currentStep === 1 && (
                                <>
                                    <Button
                                        variant="outline"
                                        onClick={handleComplete}
                                        className="px-6"
                                    >
                                        Maybe later
                                    </Button>
                                    <Button
                                        variant="primary"
                                        onClick={handleBecomeArtist}
                                        className="px-6 flex items-center gap-2 bg-gradient-to-r from-primary to-purple-500 hover:from-primary-600 hover:to-purple-600"
                                    >
                                        Become an Artist
                                        <IoMicOutline className="w-4 h-4" />
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </ModalBody>
        </Modal>
    );
}

interface FeatureCardProps {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
}

function FeatureCard({ icon: Icon, title, description }: FeatureCardProps) {
    return (
        <div className="text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700">
            <div className="flex justify-center mb-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Icon className="w-5 h-5 text-primary" />
                </div>
            </div>
            <h4 className="font-semibold text-sm mb-1">{title}</h4>
            <p className="text-xs text-gray-600 dark:text-gray-400">{description}</p>
        </div>
    );
}
