import { cn } from "@/utils";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, PropsWithChildren, useRef } from "react";
import { IoClose } from "../icons";

type Props = PropsWithChildren<{
    title?: string;
    description?: string;
    isOpen: boolean;
    closeModal: () => void;
    className?: string;
}>;

export function Modal({
    isOpen,
    closeModal,
    children,
    title,
    description,
    className,
}: Props) {
    const refDiv = useRef(null);

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog
                as="div"
                className="relative z-[999]"
                initialFocus={refDiv}
                onClose={closeModal}
            >
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className={className}>
                                <div ref={refDiv}>
                                    {title && <Dialog.Title>{title}</Dialog.Title>}
                                    {description && (
                                        <Dialog.Description>
                                            {description}
                                        </Dialog.Description>
                                    )}

                                    {children}
                                </div>
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}

type IModalBody = PropsWithChildren<{
    className?: string;
    onClose?: () => void;
}>;

export function ModalBody(props: IModalBody) {
    return (
        <div
            className={cn(
                "bg-light-primary-background dark:bg-dark-primary-background relative",
                "w-11/12 md:w-1/2 lg:w-5/12 p-10 rounded-lg",
                props.className
            )}
        >
            {props.onClose && (
                <span
                    onClick={props.onClose}
                    className="top-4 right-10 absolute cursor-pointer"
                >
                    <IoClose className="w-5 h-5 dark:text-light" />
                </span>
            )}

            {props.children}
        </div>
    );
}
